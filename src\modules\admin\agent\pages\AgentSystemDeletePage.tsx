import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { useAdminAgentSystemsTrash } from '../agent-system/hooks/useAgentSystem';
import { AgentSystemListItem, AgentStatusEnum } from '../agent-system/types/agent-system.types';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import AdminAgentCard from '../components/AdminAgentCard';

// Interface cho dữ liệu từ API trash
interface ApiAgentSystemTrashItem {
  id: string;
  name: string;
  avatar: string | null;
  model: string;
  provider: string;
  active: boolean;
}

// Interface cho response từ API trash
interface ApiAgentSystemTrashResponse {
  items: ApiAgentSystemTrashItem[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}

/**
 * Trang hiển thị danh sách System Agents đã xóa
 */
const AgentSystemDeletePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  // Query params
  const queryParams = {
    page,
    limit,
    search: search || undefined,
  };

  // Lấy danh sách system agents đã xóa
  const { data: agentsResponse, isLoading, error, refetch } = useAdminAgentSystemsTrash(queryParams);

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleBackToMain = () => {
    navigate('/admin/agent/system');
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Transform dữ liệu từ API thành format phù hợp với component
  const agents = useMemo(() => {
    const response = agentsResponse as ApiAgentSystemTrashResponse | undefined;
    if (!response?.items) {
      return [];
    }

    const apiAgents = response.items;

    return apiAgents.map((apiAgent: ApiAgentSystemTrashItem): AgentSystemListItem => ({
      id: apiAgent.id,
      name: apiAgent.name,
      nameCode: '', // API trash không trả về nameCode
      avatar: apiAgent.avatar,
      model: apiAgent.model,
      status: apiAgent.active ? AgentStatusEnum.true : AgentStatusEnum.false,
      model_id: apiAgent.model,
      type_provider: apiAgent.provider,
      active: apiAgent.active,
      isSupervisor: false, // Agents đã xóa không thể là supervisor
    }));
  }, [agentsResponse]);

  const totalItems = (agentsResponse as ApiAgentSystemTrashResponse | undefined)?.meta?.totalItems || 0;

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          items={[]}
          additionalIcons={[
            {
              icon: 'arrow-left',
              tooltip: t('admin:agent.system.backToMain', 'Quay lại danh sách chính'),
              variant: 'default',
              onClick: handleBackToMain,
              className: 'text-blue-600 hover:text-blue-800',
            }
          ]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          items={[]}
          additionalIcons={[
            {
              icon: 'arrow-left',
              tooltip: t('admin:agent.system.backToMain', 'Quay lại danh sách chính'),
              variant: 'default',
              onClick: handleBackToMain,
              className: 'text-blue-600 hover:text-blue-800',
            }
          ]}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common.error')}
          description={t('admin:agent.list.loadError')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common.retry')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        items={[]}
        additionalIcons={[
          {
            icon: 'arrow-left',
            tooltip: t('admin:agent.system.backToMain', 'Quay lại danh sách chính'),
            variant: 'default',
            onClick: handleBackToMain,
            className: 'text-blue-600 hover:text-blue-800',
          }
        ]}
      />

      {agents.length > 0 ? (
        <>
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
            gap={{ xs: 4, md: 5, lg: 6 }}
          >
            {agents.map(agent => (
              <div key={agent.id} className="h-full">
                <AdminAgentCard agent={agent} allAgents={agents} isTrashPage={true} />
              </div>
            ))}
          </ResponsiveGrid>

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="trash"
          title={t('admin:agent.trash.noAgents', 'Không có agent nào trong thùng rác')}
          description={
            search
              ? t('admin:agent.system.noSearchResults', 'Không tìm thấy kết quả phù hợp')
              : t('admin:agent.trash.noAgentsDescription', 'Thùng rác trống. Các agent đã xóa sẽ xuất hiện ở đây.')
          }
          actions={
            <Button
              variant="outline"
              onClick={handleBackToMain}
            >
              {t('admin:agent.system.backToMain', 'Quay lại danh sách chính')}
            </Button>
          }
        />
      )}
    </div>
  );
};

export default AgentSystemDeletePage;
