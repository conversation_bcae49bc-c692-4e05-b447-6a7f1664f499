import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination, SlideInForm } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { AdminAgentGrid, AddAgentSystemForm } from '../components';
import { useAdminAgentSystems } from '../agent-system/hooks/useAgentSystem';
import { AgentSystemListItem, AgentStatusEnum } from '../agent-system/types/agent-system.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { AdminAgentSystemService } from '../agent-system/services/agent-system.service';

// Interface cho dữ liệu từ API
interface ApiAgentSystemItem {
  id: string;
  name: string;
  nameCode: string;
  avatar: string | null;
  model: string;
  provider: string;
  isSupervisor: boolean;
}

// Interface cho response từ API
interface ApiAgentSystemResponse {
  items: ApiAgentSystemItem[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}

/**
 * Trang hiển thị danh sách System Agents
 */
const AgentSystemPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const { success } = useSmartNotification();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  // Form states
  const [isCreateFormVisible, setIsCreateFormVisible] = useState(false);

  // Query params
  const queryParams = {
    page,
    limit,
    search: search || undefined,
  };

  // Lấy danh sách system agents
  const { data: agentsResponse, isLoading, error, refetch } = useAdminAgentSystems(queryParams);



  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleAddAgent = () => {
    setIsCreateFormVisible(true);
  };

  // Form handlers
  const hideCreateForm = () => setIsCreateFormVisible(false);

  // Handle form submission
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmitCreateAgent = async (values: any) => {
    try {
      const agentSystemService = new AdminAgentSystemService();
      const response = await agentSystemService.createAgentSystem(values);
      return response;
    } catch (error) {
      console.error('Error creating agent system:', error);
      throw error;
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Transform dữ liệu từ API thành format phù hợp với component
  const agents = useMemo(() => {
    const response = agentsResponse as ApiAgentSystemResponse | undefined;
    if (!response?.items) {
      return [];
    }

    const apiAgents = response.items;

    return apiAgents.map((apiAgent: ApiAgentSystemItem): AgentSystemListItem => ({
      id: apiAgent.id,
      name: apiAgent.name,
      nameCode: apiAgent.nameCode,
      avatar: apiAgent.avatar,
      model: apiAgent.model,
      status: AgentStatusEnum.APPROVED, // Mặc định là APPROVED vì API không trả về status
      model_id: apiAgent.model,
      type_provider: apiAgent.provider,
      roles: apiAgent.isSupervisor ? { id: 'supervisor', name: 'Supervisor' } : undefined,
    }));
  }, [agentsResponse]);

  const totalItems = (agentsResponse as ApiAgentSystemResponse | undefined)?.meta?.totalItems || 0;

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={[]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={[]}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common.error')}
          description={t('admin:agent.list.loadError')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common.retry')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddAgent}
        items={[]}
      />

      {agents.length > 0 ? (
        <>
          <AdminAgentGrid agents={agents} />

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="cpu"
          title={t('admin:agent.list.noAgents')}
          description={
            search
              ? t('admin:agent.system.noSearchResults')
              : t('admin:agent.list.noAgentsDescription')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddAgent}
            >
              {t('admin:agent.system.addAgent')}
            </Button>
          }
        />
      )}

      {/* SlideInForm for Create Agent */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <AddAgentSystemForm
          onSubmit={handleSubmitCreateAgent}
          onCancel={hideCreateForm}
          onSuccess={() => {
            hideCreateForm();
            success({
              title: t('admin:agent.system.createSuccess', 'Thành công'),
              message: t('admin:agent.system.createSuccessMessage', 'Agent system đã được tạo thành công'),
            });
            refetch(); // Refresh the list
          }}
        />
      </SlideInForm>
    </div>
  );
};

export default AgentSystemPage;
