import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
  Slider,
  Loading,
} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';
import { apiClient } from '@/shared/api/axios';

// Types
interface UpdateAgentSystemDto {
  name: string;
  nameCode: string;
  avatarMimeType?: string;
  modelConfig: {
    temperature: number;
    top_p: number;
    top_k: number;
    max_tokens: number;
  };
  instruction: string;
  description: string;
  vectorStoreId?: string;
  modelId: string;
}

interface UpdateAgentSystemResponse {
  result?: {
    id: string;
    avatarUrlUpload?: string;
  };
}

interface VectorStore {
  storeId: string;
  storeName: string;
}

interface SystemModel {
  id: string;
  modelId: string;
  provider: string;
}

interface SystemModelsResponse {
  items?: SystemModel[];
}

interface VectorStoresResponse {
  items?: VectorStore[];
}

interface AgentSystemDetail {
  id: string;
  name: string;
  nameCode: string;
  avatar?: string;
  model: string;
  provider: string;
  instruction: string;
  description: string;
  vectorStoreId?: string;
  modelId: string;
  modelConfig: {
    temperature: number;
    top_p: number;
    top_k: number;
    max_tokens: number;
  };
}

interface EditAgentSystemFormProps {
  agentId: string;
  onSubmit: (values: UpdateAgentSystemDto) => Promise<UpdateAgentSystemResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateAgentSystemSchema = (t: any) => z.object({
  name: z.string().min(1, t('admin:agent.validation.nameRequired', 'Tên agent là bắt buộc')),
  nameCode: z.string().min(1, t('admin:agent.validation.nameCodeRequired', 'Mã định danh là bắt buộc')),
  instruction: z.string().min(1, t('admin:agent.validation.instructionRequired', 'Hướng dẫn là bắt buộc')),
  description: z.string().min(1, t('admin:agent.validation.descriptionRequired', 'Mô tả là bắt buộc')),
  vectorStoreId: z.string().optional(),
  modelId: z.string().uuid(t('admin:agent.validation.modelIdInvalid', 'Model ID phải là UUID hợp lệ')),
});

const EditAgentSystemForm: React.FC<EditAgentSystemFormProps> = ({ 
  agentId, 
  onSubmit, 
  onCancel, 
  onSuccess 
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  // Agent data state
  const [agentData, setAgentData] = useState<AgentSystemDetail | null>(null);
  
  // Avatar upload states
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);
  
  // Model config states
  const [modelConfig, setModelConfig] = useState({
    temperature: 1,
    top_p: 1,
    top_k: 1,
    max_tokens: 1000,
  });
  
  // Provider and model selection states
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(TypeProviderEnum.OPENAI);
  const [vectorStores, setVectorStores] = useState<VectorStore[]>([]);
  const [systemModels, setSystemModels] = useState<SystemModel[]>([]);
  const [loadingVectorStores, setLoadingVectorStores] = useState(false);
  const [loadingSystemModels, setLoadingSystemModels] = useState(false);

  // Load agent detail
  const loadAgentDetail = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get(`/admin/agents/system/${agentId}`, {
        tokenType: 'admin',
      });
      
      const agent = response.result as AgentSystemDetail;
      setAgentData(agent);
      
      // Set model config from agent data
      if (agent.modelConfig) {
        setModelConfig(agent.modelConfig);
      }
      
      // Set provider based on agent's provider
      const providerEnum = Object.values(TypeProviderEnum).find(
        p => p.toLowerCase() === agent.provider?.toLowerCase()
      ) || TypeProviderEnum.OPENAI;
      setSelectedProvider(providerEnum);
      
    } catch (error) {
      console.error('Error loading agent detail:', error);
    } finally {
      setIsLoading(false);
    }
  }, [agentId]);

  // Load vector stores
  const loadVectorStores = useCallback(async () => {
    setLoadingVectorStores(true);
    try {
      const response = await apiClient.get('/admin/vector-stores', {
        params: { ownerType: 'ADMIN', page: 1, limit: 100 },
        tokenType: 'admin',
      });
      const result = response.result as VectorStoresResponse;
      if (result?.items) {
        setVectorStores(result.items);
      }
    } catch (error) {
      console.error('Error loading vector stores:', error);
    } finally {
      setLoadingVectorStores(false);
    }
  }, []);

  // Load system models
  const loadSystemModels = useCallback(async () => {
    setLoadingSystemModels(true);
    try {
      const response = await apiClient.get('/admin/system-models', {
        params: {
          page: 1,
          limit: 100,
          provider: selectedProvider
        },
        tokenType: 'admin',
      });

      const result = response.result as SystemModelsResponse;

      if (result?.items && Array.isArray(result.items)) {
        setSystemModels(result.items);
      } else if (Array.isArray(result)) {
        setSystemModels(result);
      } else {
        console.warn('Unexpected response structure for system models:', response);
        setSystemModels([]);
      }
    } catch (error) {
      console.error('Error loading system models:', error);
      setSystemModels([]);
    } finally {
      setLoadingSystemModels(false);
    }
  }, [selectedProvider]);

  // Load data on mount
  useEffect(() => {
    loadAgentDetail();
    loadVectorStores();
  }, [loadAgentDetail, loadVectorStores]);

  // Reload models when provider changes
  useEffect(() => {
    loadSystemModels();
  }, [loadSystemModels]);

  // Handle provider selection
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
    setSystemModels([]);
  };

  // Handle model config changes
  const handleModelConfigChange = (key: keyof typeof modelConfig, value: number) => {
    setModelConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle avatar file selection
  const handleAvatarChange = useCallback((files: FileWithMetadata[]) => {
    if (files.length > 0) {
      setAvatarFiles([files[0]]);
    } else {
      setAvatarFiles([]);
    }
  }, []);

  // Upload image file to S3 using presigned URL
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    const response = await fetch(presignedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    });

    if (!response.ok) {
      throw new Error(`Failed to upload image: ${response.status} ${response.statusText}`);
    }

    return response;
  };

  // Default values for the form - sử dụng agentData nếu có, nếu không thì empty string
  const defaultValues = React.useMemo(() => ({
    name: agentData?.name || '',
    nameCode: agentData?.nameCode || '',
    instruction: agentData?.instruction || '',
    description: agentData?.description || '',
    vectorStoreId: agentData?.vectorStoreId || '',
    modelId: agentData?.modelId || '',
  }), [agentData]);

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    setIsSubmitting(true);

    try {
      // Prepare form data
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateData: any = {
        name: values.name as string,
        nameCode: values.nameCode as string,
        avatarMimeType: avatarFiles.length > 0 ? avatarFiles[0].file.type : undefined,
        modelConfig,
        instruction: values.instruction as string,
        description: values.description as string,
        vectorStoreId: values.vectorStoreId as string || undefined,
      };

      // Chỉ thêm modelId nếu có giá trị và là UUID hợp lệ
      if (values.modelId) {
        updateData.modelId = values.modelId as string;
      }

      console.log('Updating agent data:', updateData);

      // Submit form data
      const updateResult = await onSubmit(updateData);
      console.log('Agent updated successfully:', updateResult);

      // Upload avatar if provided
      const allUploadPromises: Promise<void>[] = [];

      if (avatarFiles.length > 0 && updateResult.result?.avatarUrlUpload) {
        console.log('🔍 Starting avatar upload...');

        const avatarFile = avatarFiles[0].file;
        const uploadUrl = updateResult.result.avatarUrlUpload;

        const avatarUploadPromise = (async () => {
          try {
            await uploadImageFile(avatarFile, uploadUrl);
            console.log('✅ Avatar uploaded successfully');
          } catch (error) {
            console.error('❌ Exception uploading avatar:', error);
            throw error;
          }
        })();
        allUploadPromises.push(avatarUploadPromise);
      }

      // Đợi tất cả uploads hoàn thành
      if (allUploadPromises.length > 0) {
        try {
          await Promise.all(allUploadPromises);
          console.log('🎉 All uploads completed successfully');
        } catch (uploadError) {
          console.error('❌ Upload error:', uploadError);
          throw new Error(`Upload failed: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
        }
      }

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error updating agent form:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Danh sách providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GOOGLE, name: 'Google' },
    { type: TypeProviderEnum.META, name: 'Meta' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  // Early returns after all hooks
  if (isLoading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </Card>
    );
  }

  if (!agentData) {
    return (
      <Card>
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted">
            {t('admin:agent.edit.notFound', 'Không tìm thấy agent')}
          </Typography>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h4" className="font-semibold">
          {t('admin:agent.system.editAgent', 'Chỉnh sửa Agent System')}
        </Typography>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          disabled={isSubmitting}
          leftIcon={<Icon name="x" size="sm" />}
        >
          {t('common.close', 'Đóng')}
        </Button>
      </div>

      <Form
        schema={updateAgentSystemSchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem
              name="name"
              label={t('admin:agent.form.name', 'Tên Agent')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.form.namePlaceholder', 'Nhập tên agent')}
              />
            </FormItem>

            <FormItem
              name="nameCode"
              label={t('admin:agent.form.nameCode', 'Mã định danh')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.form.nameCodePlaceholder', 'Nhập mã định danh')}
              />
            </FormItem>
          </FormGrid>

          <FormItem
            name="instruction"
            label={t('admin:agent.form.instruction', 'Hướng dẫn')}
            required
          >
            <Textarea
              fullWidth
              rows={4}
              placeholder={t('admin:agent.form.instructionPlaceholder', 'Nhập hướng dẫn cho agent')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('admin:agent.form.description', 'Mô tả')}
            required
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('admin:agent.form.descriptionPlaceholder', 'Nhập mô tả agent')}
            />
          </FormItem>
        </div>

        <Divider />

        {/* Avatar Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.avatar', 'Avatar')}
          </Typography>

          {/* Hiển thị avatar hiện tại */}
          {agentData.avatar && (
            <div className="mb-4">
              <Typography variant="body2" className="text-muted mb-2">
                {t('admin:agent.form.currentAvatar', 'Avatar hiện tại')}
              </Typography>
              <img
                src={agentData.avatar}
                alt={agentData.name}
                className="w-16 h-16 rounded-full object-cover border"
              />
            </div>
          )}

          <MultiFileUpload
            label={t('admin:agent.form.avatarUpload', 'Tải lên avatar mới')}
            accept="image/jpeg,image/png"
            placeholder={t('admin:agent.form.avatarHelp', 'Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)')}
            value={avatarFiles}
            onChange={handleAvatarChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>

        <Divider />

        {/* Model Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.modelConfig', 'Cấu hình Model')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.temperature', 'Temperature')}
              </label>
              <Slider
                value={modelConfig.temperature}
                min={0}
                max={2}
                step={0.1}
                onChange={(value) => handleModelConfigChange('temperature', value)}
                showValue
                className="w-full"
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.topP', 'Top P')}
              </label>
              <Slider
                value={modelConfig.top_p}
                min={0}
                max={1}
                step={0.1}
                onChange={(value) => handleModelConfigChange('top_p', value)}
                showValue
                className="w-full"
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.topK', 'Top K')}
              </label>
              <Slider
                value={modelConfig.top_k}
                min={1}
                max={100}
                step={1}
                onChange={(value) => handleModelConfigChange('top_k', value)}
                showValue
                className="w-full"
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.maxTokens', 'Max Tokens')}
              </label>
              <Slider
                value={modelConfig.max_tokens}
                min={100}
                max={4000}
                step={100}
                onChange={(value) => handleModelConfigChange('max_tokens', value)}
                showValue
                className="w-full"
              />
            </div>
          </FormGrid>
        </div>

        <Divider />

        {/* Provider Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.provider', 'Nhà cung cấp')}
          </Typography>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {providers.map((provider) => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
              />
            ))}
          </div>
        </div>

        <Divider />

        {/* Model and Vector Store Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.modelAndStore', 'Model và Vector Store')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem
              name="modelId"
              label={t('admin:agent.form.model', 'Model')}
              required
            >
              <Select
                fullWidth
                placeholder={t('admin:agent.form.selectModel', 'Chọn model')}
                loading={loadingSystemModels}
                options={systemModels.map(model => ({
                  value: model.id,
                  label: model.modelId,
                }))}
              />
            </FormItem>

            <FormItem
              name="vectorStoreId"
              label={t('admin:agent.form.vectorStore', 'Vector Store')}
            >
              <Select
                fullWidth
                placeholder={t('admin:agent.form.selectVectorStore', 'Chọn vector store (tùy chọn)')}
                loading={loadingVectorStores}
                options={vectorStores.map(store => ({
                  value: store.storeId,
                  label: store.storeName,
                }))}
                
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting}
            disabled={isSubmitting}
          >
            {t('admin:agent.system.updateAgent', 'Cập nhật Agent')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditAgentSystemForm;
