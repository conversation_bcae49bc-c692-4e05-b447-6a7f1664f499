import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
  Slider,
  Checkbox,
} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';
import { apiClient } from '@/shared/api/axios';

// Types
interface CreateAgentSystemDto {
  name: string;
  nameCode: string;
  avatarMimeType?: string;
  modelConfig: {
    temperature: number;
    top_p: number;
    top_k: number;
    max_tokens: number;
  };
  instruction: string;
  description: string;
  vectorStoreId?: string;
  modelId: string;
  mcpId?: string[];
  isSupervisor: boolean;
}

interface CreateAgentSystemResponse {
  result?: {
    uploadUrls?: {
      avatarUploadUrl?: {
        url: string;
      };
    };
  };
}

interface VectorStore {
  storeId: string;
  storeName: string;
}

interface SystemModel {
  id: string;
  modelId: string;
  provider: string;
}

interface AddAgentSystemFormProps {
  onSubmit: (values: CreateAgentSystemDto) => Promise<CreateAgentSystemResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createAgentSystemSchema = (t: any) => z.object({
  name: z.string().min(1, t('admin:agent.validation.nameRequired', 'Tên agent là bắt buộc')),
  nameCode: z.string().min(1, t('admin:agent.validation.nameCodeRequired', 'Mã định danh là bắt buộc')),
  instruction: z.string().min(1, t('admin:agent.validation.instructionRequired', 'Hướng dẫn là bắt buộc')),
  description: z.string().min(1, t('admin:agent.validation.descriptionRequired', 'Mô tả là bắt buộc')),
  vectorStoreId: z.string().optional(),
  modelId: z.string().min(1, t('admin:agent.validation.modelRequired', 'Model là bắt buộc')),
  isSupervisor: z.boolean().optional(),
});

const AddAgentSystemForm: React.FC<AddAgentSystemFormProps> = ({ onSubmit, onCancel, onSuccess }) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Avatar upload states
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);
  
  // Model config states
  const [modelConfig, setModelConfig] = useState({
    temperature: 1,
    top_p: 1,
    top_k: 1,
    max_tokens: 1000,
  });
  
  // Provider and model selection states
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(TypeProviderEnum.OPENAI);
  const [vectorStores, setVectorStores] = useState<VectorStore[]>([]);
  const [systemModels, setSystemModels] = useState<SystemModel[]>([]);
  const [filteredModels, setFilteredModels] = useState<SystemModel[]>([]);
  const [loadingVectorStores, setLoadingVectorStores] = useState(false);
  const [loadingSystemModels, setLoadingSystemModels] = useState(false);

  // Default values for the form
  const defaultValues = React.useMemo(() => ({
    name: '',
    nameCode: '',
    instruction: '',
    description: '',
    vectorStoreId: '',
    modelId: '',
    isSupervisor: false,
  }), []);

  // Load vector stores
  const loadVectorStores = useCallback(async () => {
    setLoadingVectorStores(true);
    try {
      const response = await apiClient.get('/admin/vector-stores', {
        params: { ownerType: 'ADMIN', page: 1, limit: 100 },
        tokenType: 'admin',
      });
      if (response.result?.items) {
        setVectorStores(response.result.items);
      }
    } catch (error) {
      console.error('Error loading vector stores:', error);
    } finally {
      setLoadingVectorStores(false);
    }
  }, []);

  // Load system models
  const loadSystemModels = useCallback(async () => {
    setLoadingSystemModels(true);
    try {
      const response = await apiClient.get('/admin/system-models', {
        params: { page: 1, limit: 100 },
        tokenType: 'admin',
      });
      if (response.result?.items) {
        setSystemModels(response.result.items);
      }
    } catch (error) {
      console.error('Error loading system models:', error);
    } finally {
      setLoadingSystemModels(false);
    }
  }, []);

  // Filter models by selected provider
  useEffect(() => {
    const filtered = systemModels.filter(model => model.provider === selectedProvider);
    setFilteredModels(filtered);
  }, [systemModels, selectedProvider]);

  // Load data on mount
  useEffect(() => {
    loadVectorStores();
    loadSystemModels();
  }, [loadVectorStores, loadSystemModels]);

  // Handle provider selection
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
  };

  // Handle model config changes
  const handleModelConfigChange = (key: keyof typeof modelConfig, value: number) => {
    setModelConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle avatar file selection
  const handleAvatarChange = useCallback((files: FileWithMetadata[]) => {
    setAvatarFiles(files);
  }, []);

  // Upload image file to S3
  const uploadImageFile = async (file: File, uploadUrl: string): Promise<void> => {
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }
  };

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    setIsSubmitting(true);
    
    try {
      // Prepare form data
      const agentData: CreateAgentSystemDto = {
        name: values.name as string,
        nameCode: values.nameCode as string,
        avatarMimeType: avatarFiles.length > 0 ? avatarFiles[0].file.type : undefined,
        modelConfig,
        instruction: values.instruction as string,
        description: values.description as string,
        vectorStoreId: values.vectorStoreId as string || undefined,
        modelId: values.modelId as string,
        mcpId: [], // TODO: Add MCP selection if needed
        isSupervisor: values.isSupervisor as boolean || false,
      };

      console.log('Submitting agent data:', agentData);

      // Submit form data
      const createResult = await onSubmit(agentData);
      console.log('Agent created successfully:', createResult);

      // Upload avatar if provided
      if (avatarFiles.length > 0 && createResult.result?.uploadUrls?.avatarUploadUrl?.url) {
        const avatarFile = avatarFiles[0].file;
        const uploadUrl = createResult.result.uploadUrls.avatarUploadUrl.url;
        
        console.log('Uploading avatar:', {
          fileName: avatarFile.name,
          fileSize: avatarFile.size,
          fileType: avatarFile.type,
          uploadUrl,
        });

        try {
          await uploadImageFile(avatarFile, uploadUrl);
          console.log('Avatar uploaded successfully');
        } catch (error) {
          console.error('Error uploading avatar:', error);
          throw error;
        }
      }

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error submitting agent form:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Danh sách providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GOOGLE, name: 'Google' },
    { type: TypeProviderEnum.META, name: 'Meta' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  return (
    <Card className="p-6 w-full max-w-4xl">
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h4" className="font-semibold">
          {t('admin:agent.system.addAgent', 'Thêm Agent System')}
        </Typography>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          disabled={isSubmitting}
          leftIcon={<Icon name="x" size="sm" />}
        >
          {t('common.close', 'Đóng')}
        </Button>
      </div>

      <Form
        schema={createAgentSystemSchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem
              name="name"
              label={t('admin:agent.form.name', 'Tên Agent')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.form.namePlaceholder', 'Nhập tên agent')}
              />
            </FormItem>

            <FormItem
              name="nameCode"
              label={t('admin:agent.form.nameCode', 'Mã định danh')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.form.nameCodePlaceholder', 'Nhập mã định danh')}
              />
            </FormItem>
          </FormGrid>

          <FormItem
            name="instruction"
            label={t('admin:agent.form.instruction', 'Hướng dẫn')}
            required
          >
            <Textarea
              fullWidth
              rows={4}
              placeholder={t('admin:agent.form.instructionPlaceholder', 'Nhập hướng dẫn cho agent')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('admin:agent.form.description', 'Mô tả')}
            required
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('admin:agent.form.descriptionPlaceholder', 'Nhập mô tả agent')}
            />
          </FormItem>
        </div>

        <Divider />

        {/* Avatar Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.avatar', 'Avatar')}
          </Typography>

          <MultiFileUpload
            label={t('admin:agent.form.avatarUpload', 'Tải lên avatar')}
            accept="image/jpeg,image/png"
            placeholder={t('admin:agent.form.avatarHelp', 'Hỗ trợ định dạng: JPG, PNG')}
            value={avatarFiles}
            onChange={handleAvatarChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>

        <Divider />

        {/* Model Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.modelConfig', 'Cấu hình Model')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.temperature', 'Temperature')}
              </label>
              <Slider
                value={modelConfig.temperature}
                min={0}
                max={2}
                step={0.1}
                onValueChange={(value) => handleModelConfigChange('temperature', value)}
                valueSuffix=""
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.topP', 'Top P')}
              </label>
              <Slider
                value={modelConfig.top_p}
                min={0}
                max={1}
                step={0.1}
                onValueChange={(value) => handleModelConfigChange('top_p', value)}
                valueSuffix=""
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.topK', 'Top K')}
              </label>
              <Slider
                value={modelConfig.top_k}
                min={1}
                max={100}
                step={1}
                onValueChange={(value) => handleModelConfigChange('top_k', value)}
                valueSuffix=""
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.maxTokens', 'Max Tokens')}
              </label>
              <Slider
                value={modelConfig.max_tokens}
                min={1}
                max={4096}
                step={1}
                onValueChange={(value) => handleModelConfigChange('max_tokens', value)}
                valueSuffix=""
              />
            </div>
          </FormGrid>
        </div>

        <Divider />

        {/* Provider Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.provider', 'Loại Provider')}
          </Typography>

          <div className="flex flex-nowrap gap-3 overflow-x-auto pb-2">
            {providers.map((provider) => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
                disabled={isSubmitting}
              />
            ))}
          </div>
        </div>

        <Divider />

        {/* Model and Vector Store Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.resources', 'Tài nguyên')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem
              name="modelId"
              label={t('admin:agent.form.model', 'Model')}
              required
            >
              <Select
                fullWidth
                loading={loadingSystemModels}
                placeholder={t('admin:agent.form.selectModel', 'Chọn model')}
                options={filteredModels.map(model => ({
                  value: model.id,
                  label: model.modelId,
                }))}
              />
            </FormItem>

            <FormItem
              name="vectorStoreId"
              label={t('admin:agent.form.vectorStore', 'Vector Store')}
            >
              <Select
                fullWidth
                loading={loadingVectorStores}
                placeholder={t('admin:agent.form.selectVectorStore', 'Chọn vector store')}
                options={vectorStores.map(store => ({
                  value: store.storeId,
                  label: store.storeName,
                }))}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Supervisor Option */}
        <div className="space-y-4">
          <FormItem name="isSupervisor">
            <Checkbox
              label={t('admin:agent.form.isSupervisor', 'Là Supervisor')}
            />
          </FormItem>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={isSubmitting}
          >
            {t('admin:agent.form.create', 'Tạo Agent')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AddAgentSystemForm;
