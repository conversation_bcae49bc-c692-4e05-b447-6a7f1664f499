import React, { useState } from 'react';
import { Card, IconCard, Toolt<PERSON>, Chip, Modal, Button, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentSystemListItem } from '../agent-system/types/agent-system.types';
import {
  useDeleteAdminAgentSystem,
  useToggleActiveAdminAgentSystem,
  useSetSupervisorAdminAgentSystem,
  useRestoreAdminAgentSystem
} from '../agent-system/hooks/useAgentSystem';
import { NotificationUtil } from '@/shared/utils/notification';

interface AdminAgentCardProps {
  agent: AgentSystemListItem;
  /** Danh sách tất cả agents để kiểm tra supervisor */
  allAgents?: AgentSystemListItem[];
  /** Có phải là trang trash không */
  isTrashPage?: boolean;
}

/**
 * Component hiển thị thông tin của một Admin Agent (đơn giản hóa, không có level/exp)
 */
const AdminAgentCard: React.FC<AdminAgentCardProps> = ({ agent, allAgents = [], isTrashPage = false }) => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAdminAgentSystem();
  const toggleActiveMutation = useToggleActiveAdminAgentSystem();
  const setSupervisorMutation = useSetSupervisorAdminAgentSystem();
  const restoreAgentMutation = useRestoreAdminAgentSystem();

  // Kiểm tra xem có agent nào đang là supervisor không
  const currentSupervisor = allAgents.find(a => a.isSupervisor);
  const hasCurrentSupervisor = !!currentSupervisor;
  const isCurrentSupervisor = agent.isSupervisor;

  const handleEditAgent = () => {
    // Navigate đến trang edit agent
    navigate(`/admin/agent/system/${agent.id}/edit`);
  };

  const handleToggleActive = async () => {
    try {
      await toggleActiveMutation.mutateAsync(agent.id);

      NotificationUtil.success({
        message: t('admin:agent.card.updateSuccess'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error toggling agent active status:', error);
      NotificationUtil.error({
        message: t('admin:agent.card.updateError'),
        duration: 5000,
      });
    }
  };

  const handleToggleSupervisor = async () => {
    try {
      await setSupervisorMutation.mutateAsync(agent.id);

      NotificationUtil.success({
        message: isCurrentSupervisor
          ? t('admin:agent.card.removeSupervisorSuccess', 'Đã bỏ quyền supervisor thành công')
          : t('admin:agent.card.setSupervisorSuccess', 'Đã set làm supervisor thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error toggling supervisor status:', error);
      NotificationUtil.error({
        message: t('admin:agent.card.supervisorError', 'Có lỗi xảy ra khi thay đổi quyền supervisor'),
        duration: 5000,
      });
    }
  };

  const handleRestoreAgent = async () => {
    try {
      await restoreAgentMutation.mutateAsync(agent.id);

      NotificationUtil.success({
        message: t('admin:agent.card.restoreSuccess', 'Đã khôi phục agent thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error restoring agent:', error);
      NotificationUtil.error({
        message: t('admin:agent.card.restoreError', 'Có lỗi xảy ra khi khôi phục agent'),
        duration: 5000,
      });
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      NotificationUtil.success({
        message: t('admin:agent.card.deleteSuccess'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting agent:', error);
      NotificationUtil.error({
        message: t('admin:agent.card.deleteError'),
        duration: 5000,
      });
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Xác định variant cho model chip dựa trên nhà cung cấp
  const getModelVariant = (
    model: string
  ): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    const modelLower = model.toLowerCase();

    // OpenAI models
    if (modelLower.includes('gpt')) return 'danger';

    // Anthropic models
    if (modelLower.includes('claude')) return 'success';

    // Google models
    if (modelLower.includes('gemini')) return 'info';

    // DeepSeek models
    if (modelLower.includes('deepseek')) return 'warning';

    // Mistral models
    if (modelLower.includes('mistral')) return 'primary';

    // Llama models
    if (modelLower.includes('llama')) return 'info';

    // Default for other models
    return 'primary';
  };

  return (
    <>
      <Card
        className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-4">
          <div className="flex flex-col space-y-4">
            {/* Hàng 1: Avatar, tên, và model */}
            <div className="flex items-center gap-3 overflow-hidden">
              {/* Avatar và Supervisor Toggle */}
              <div className="flex items-center gap-2">
                <div className="relative w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0">
                  <div className="w-full h-full relative">
                    <img
                      src="/assets/images/frame-level-agents.png"
                      alt="Level frame"
                      className="absolute inset-0 w-full h-full object-contain z-10"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-0">
                      <img
                        src={agent.avatar || '/assets/images/default-avatar.png'}
                        alt={agent.name}
                        className="w-[75%] h-[75%] rounded-full"
                      />
                    </div>
                    {/* Chỉ báo trạng thái active */}
                    <div
                      className={`absolute bottom-1 right-1 w-3 h-3 rounded-full z-20 ${
                        agent.active ? 'bg-green-500 dark:bg-green-400' : 'bg-gray-300 dark:bg-gray-600'
                      }`}
                    />
                    {/* Role badge */}
                    {agent.roles && (
                      <div className="absolute -top-1 -right-1 z-20">
                        <Chip variant="warning" size="sm" className="text-xs px-1">
                          {agent.roles.name.substring(0, 2).toUpperCase()}
                        </Chip>
                      </div>
                    )}
                  </div>
                </div>

                {/* Supervisor Toggle - hiển thị khi không có supervisor hoặc agent này đang là supervisor */}
             
              </div>

              {/* Thông tin agent: tên và model */}
              <div className="flex flex-col min-w-0 flex-grow">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                  <div className="min-w-0">
                    <Typography variant="h5" className="font-semibold text-gray-900 dark:text-white truncate">
                      {agent.name}
                    </Typography>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {agent.type_provider || 'Unknown'}
                    </div>
                  </div>
                  <div className="flex-shrink-0 mt-1 sm:mt-0">
                    <Chip
                      variant={getModelVariant(agent.model)}
                      size="sm"
                      className="font-normal max-w-full truncate"
                    >
                      {agent.model}
                    </Chip>
                  </div>
                </div>
              </div>
            </div>

            {/* Hàng 2: Các nút chức năng */}
            <div className="flex justify-between">
              <div>
                {/* Supervisor toggle - chỉ hiển thị trong trang chính */}
                {!isTrashPage && (!hasCurrentSupervisor || isCurrentSupervisor) && (
                  <Tooltip
                    content={isCurrentSupervisor
                      ? t('admin:agent.card.removeSupervisor', 'Bỏ quyền Supervisor')
                      : t('admin:agent.card.setSupervisor', 'Set làm Supervisor')
                    }
                    position="top"
                  >
                    <IconCard
                      icon={isCurrentSupervisor ? "toggle-on" : "toggle-off"}
                      variant={isCurrentSupervisor ? 'primary' : 'default'}
                      size="sm"
                      onClick={handleToggleSupervisor}
                      className={isCurrentSupervisor ? 'text-blue-500' : 'text-gray-400'}
                      disabled={setSupervisorMutation.isPending}
                    />
                  </Tooltip>
                )}
              </div>

              <div className="flex space-x-6">
                {isTrashPage ? (
                  // Chỉ hiển thị nút restore trong trang trash
                  <Tooltip content={t('admin:agent.card.restore', 'Khôi phục')} position="top">
                    <IconCard
                      icon="chevron-up"
                      variant="primary"
                      size="md"
                      onClick={handleRestoreAgent}
                      className="text-green-500 hover:text-green-600"
                      disabled={restoreAgentMutation.isPending}
                    />
                  </Tooltip>
                ) : (
                  // Hiển thị các nút thông thường trong trang chính
                  <>
                    <Tooltip
                      content={agent.active ? t('admin:agent.card.deactivate') : t('admin:agent.card.activate')}
                      position="top"
                    >
                      <IconCard
                        icon="power"
                        variant={agent.active ? 'primary' : 'default'}
                        size="md"
                        onClick={handleToggleActive}
                        className={agent.active ? 'text-green-500' : 'text-gray-400'}
                        disabled={toggleActiveMutation.isPending}
                      />
                    </Tooltip>
                    <Tooltip content={t('admin:agent.card.edit')} position="top">
                      <IconCard icon="edit" variant="default" size="md" onClick={handleEditAgent} />
                    </Tooltip>
                    <Tooltip content={t('admin:agent.card.delete')} position="top">
                      <IconCard
                        icon="trash"
                        variant="default"
                        size="md"
                        onClick={handleDeleteClick}
                        className="text-red-500 hover:text-red-600"
                        disabled={deleteAgentMutation.isPending}
                      />
                    </Tooltip>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('admin:agent.card.confirmDelete')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={handleDeleteCancel}
              disabled={deleteAgentMutation.isPending}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
              isLoading={deleteAgentMutation.isPending}
            >
              {t('admin:agent.card.delete')}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-300">
            {t('admin:agent.card.deleteMessage')}
          </p>

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={agent.avatar || '/assets/images/default-avatar.png'}
                alt={agent.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{agent.type_provider || 'Unknown'}</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AdminAgentCard;
