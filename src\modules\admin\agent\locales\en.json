{"agent": {"management": {"title": "Agent Management", "description": "Comprehensive Agent system management"}, "rank": {"title": "Admin - Agent Rank", "description": "Agent ranking management"}, "system": {"title": "Admin - Agent System", "description": "System Agent management", "pageTitle": "System Agent Management", "addAgent": "Add New Agent", "searchPlaceholder": "Search Agents...", "noSearchResults": "No Agents found matching your search criteria.", "createFirst": "Create First System Agent"}, "user": {"title": "Admin - Agent User", "description": "User Agent management"}, "type": {"title": "Admin - Type Agent", "description": "Admin type agent management endpoints"}, "list": {"title": "Agent List", "noAgents": "No Agents", "noAgentsDescription": "There are currently no Agents in the system.", "loadError": "Unable to load Agent list. Please try again.", "loading": "Loading Agent list...", "refreshing": "Refreshing data..."}, "card": {"supervisor": "Supervisor", "active": "Active", "inactive": "Inactive", "activate": "Activate", "deactivate": "Deactivate", "edit": "Edit", "delete": "Delete", "confirmDelete": "Confirm Delete Agent", "deleteMessage": "Are you sure you want to delete this Agent? This action cannot be undone.", "deleteSuccess": "Agent deleted successfully", "deleteError": "An error occurred while deleting the Agent", "updateSuccess": "Agent updated successfully", "updateError": "An error occurred while updating the Agent"}, "pagination": {"itemsPerPage": "Items per page", "showingItems": "Showing {from} - {to} of {total} items", "page": "Page", "of": "of", "previous": "Previous", "next": "Next"}, "form": {"basicInfo": "Basic Information", "name": "Agent Name", "namePlaceholder": "Enter agent name", "nameCode": "Identifier Code", "nameCodePlaceholder": "Enter identifier code", "instruction": "Instructions", "instructionPlaceholder": "Enter instructions for agent", "description": "Description", "descriptionPlaceholder": "Enter agent description", "avatar": "Avatar", "avatarUpload": "Upload avatar", "avatarHelp": "Supported formats: JPG, PNG (single image only)", "modelConfig": "Model Configuration", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Provider Type", "resources": "Resources", "model": "Model", "selectModel": "Select model", "vectorStore": "Vector Store", "selectVectorStore": "Select vector store", "isSupervisor": "Is Supervisor", "create": "Create Agent", "creating": "Creating Agent...", "createSuccess": "Agent created successfully", "createError": "An error occurred while creating the Agent", "uploadingAvatar": "Uploading avatar...", "uploadAvatarSuccess": "Avatar uploaded successfully", "uploadAvatarError": "An error occurred while uploading avatar"}, "validation": {"nameRequired": "Agent name is required", "nameCodeRequired": "Identifier code is required", "instructionRequired": "Instructions are required", "descriptionRequired": "Description is required", "modelRequired": "Model is required"}}}